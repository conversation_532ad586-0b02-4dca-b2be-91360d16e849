// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"description": "水电燃气记录表",
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": true,
		"create": "auth.uid != null",
		"update": "auth.uid != null",
		"delete": "auth.uid != null"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"room_id": {
			"bsonType": "string",
			"description": "房间id",
			"label": "房间ID",
			"foreignKey": "fangke_room._id"
		},
		"building_id":{
			"bsonType": "string",
			"description": "楼房id",
			"label": "楼房ID",
			"foreignKey": "fangke_building._id"
		},
		"create_time": {
			"bsonType": "timestamp",
			"forceDefaultValue": {
				"$env": "now"
			}
		},
		"update_time": {
			"bsonType": "timestamp",
			"forceDefaultValue": {
				"$env": "now"
			}
		},
		"type": {
			"bsonType": "int",
			"description": "类型：1.水 2.电 3.燃气",
			"label": "类型",
			"enum": [{
					"value": 1,
					"text": "水表"
				},
				{
					"value": 2,
					"text": "电表"
				},
				{
					"value": 3,
					"text": "燃气"
				}
			]
		},
		"date":{
			"bsonType": "string",
			"label": "年-月",
			"description": "记录表的日期，格式为YYYY-MM"
		},
		"record_date":{
			"bsonType": "timestamp",
			"label": "记录时间戳",
			"description": "记录的时间，精确到秒"
		},
		"last_num":{
			"bsonType": "int",
			"description": "上月使用量（精确到分）",
			"label": "上月使用量",
			"defaultValue":0
		},
		"num":{
			"bsonType": "int",
			"description": "当月使用量（精确到分）",
			"label": "当月使用量",
			"defaultValue":0
		},
		"is_submit":{
			"bsonType": "bool",
			"label": "是否已提交",
			"description": "判断是否已经进入账单，如果已进入，则不能再改",
			"defaultValue":false
		},
		"is_update":{
			"bsonType": "bool",
			"label": "是否已抄",
			"description": "用于判断是否有抄表操作，用于用户查看本月是否抄表",
			"defaultValue":false
		},
		"max_num":{
			"bsonType": "int",
			"label": "默认表数最大值（分）",
			"defaultValue":9999999
		},
		"room_name":{
			"bsonType": "string",
			"description": "房间名字",
			"label": "房间名字",
			"defaultValue":""
		},
		"usage":{
			"bsonType": "int",
			"description": "使用量（精确到分）",
			"label": "使用量",
			"defaultValue":0
		},
		"unit":{
			"bsonType": "int",
			"description": "单位",
			"label": "单位",
			"defaultValue":0
		},
		"submit_time":{
			"bsonType": "string",
			"label": "账单生成时间",
			"description": "账单生成时间，格式为YYYY-MM-DD"
		}

	}
}