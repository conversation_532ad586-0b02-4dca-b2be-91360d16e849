{"bsonType": "object", "required": ["building_id", "building_name", "name"], "permission": {"read": "auth.uid != null", "create": "auth.uid != null", "update": "auth.uid != null", "delete": "auth.uid != null"}, "properties": {"_id": {"description": "ID，系统自动生成,房间ID，不能重复"}, "uid": {"bsonType": "string", "title": "业主id", "label": "业主id", "foreignKey": "uni-id-users._id"}, "building_id": {"title": "房子ID", "bsonType": "string", "description": "房子ID,归属哪个房子的", "foreignKey": "fangke_building._id", "enum": {"collection": "fangke_building", "field": "_id as value, name as text"}}, "building_name": {"title": "房子名字", "bsonType": "string", "description": "房子名字", "defaultValue": ""}, "name": {"bsonType": "string", "title": "房门号", "description": "房门号", "trim": "right", "defaultValue": ""}, "desc": {"title": "房间备注", "label": "房间备注", "bsonType": "string", "description": "房间备注", "defaultValue": ""}, "devices": {"title": "房间设备", "bsonType": "array", "description": "房间的家具配置", "arrayType": "int"}, "rent": {"title": "租金", "bsonType": "int", "description": "租金", "defaultValue": 0}, "rent_status": {"bsonType": "int", "title": "出租状态", "description": "出租状态：0 未出租 1.已出租 2.占用", "label": "出租状态", "defaultValue": 0, "enum": [{"value": 0, "text": "未出租"}, {"value": 1, "text": "已出租"}, {"value": 2, "text": "占用"}]}, "room_status": {"bsonType": "array", "title": "业务状态", "description": "业务状态：0.空房 1.已结算 2.待缴费 3.欠费超时 4.租约即将到期 5.租约到期 6.待结算 7.已预定 8.预定过期  9.待结算未预定 10.待结算已预定 11.租约待审核 12.退租待审核 ", "arrayType": "int", "label": "业务状态", "defaultValue": 0, "enum": [{"value": 0, "text": "空"}, {"value": 1, "text": "完"}, {"value": 2, "text": "待"}, {"value": 3, "text": "欠"}, {"value": 4, "text": "快"}, {"value": 5, "text": "逾"}, {"value": 6, "text": "结"}, {"value": 7, "text": "定"}, {"value": 8, "text": "过"}, {"value": 9, "text": "结未"}, {"value": 10, "text": "结定"}, {"value": 11, "text": "审"}, {"value": 12, "text": "退审"}]}, "idle_num": {"bsonType": "int", "label": "空置天数", "description": "空置天数", "defaultValue": 0}, "arrears_num": {"bsonType": "int", "label": "拖欠天数", "description": "拖欠天数", "defaultValue": 0}, "overday_num": {"bsonType": "int", "label": "逾期天数", "description": "逾期天数", "defaultValue": 0}, "arrears": {"bsonType": "int", "label": "逾期欠费", "description": "一共逾期欠费的金额", "defaultValue": 0}, "images": {"bsonType": "array", "description": "房间照片URL数组", "label": "房间照片", "arrayType": "string"}, "contract": {"bsonType": "string", "title": "合同id", "defaultValue": "", "foreignKey": "fangke_contracts._id"}, "start_time": {"title": "入住时间", "bsonType": "string", "description": "入住时间", "defaultValue": ""}, "end_time": {"title": "到期时间", "bsonType": "string", "description": "合同到期时间", "defaultValue": ""}, "update_time": {"bsonType": "timestamp", "title": "更新时间", "description": "更新时间", "defaultValue": {"$env": "now"}}, "update_ip": {"bsonType": "string", "title": "发布时IP地址", "description": "发表时 IP 地址", "forceDefaultValue": {"$env": "clientIP"}}, "sum": {"title": "当月应收租金", "bsonType": "int", "description": "当月应收的租金（分）", "defaultValue": 0}, "day": {"title": "收租日期", "bsonType": "int", "description": "收租日期几号", "defaultValue": 0}, "deposit": {"title": "押金", "bsonType": "int", "description": "押金", "defaultValue": 0}, "floor": {"title": "所在楼层", "bsonType": "int", "description": "所在的楼层", "defaultValue": 0}, "layout": {"title": "实际房型", "bsonType": "string", "description": "实际房型", "defaultValue": ""}, "area": {"title": "面积", "bsonType": "int", "description": "面积", "defaultValue": 0}, "payment": {"title": "付租方式", "bsonType": "string", "description": "付租方式", "defaultValue": ""}, "booked": {"title": "是否预定", "bsonType": "bool", "defaultValue": false, "label": "是否预定"}, "address": {"title": "房间地址", "bsonType": "string", "label": "地址", "defaultValue": ""}, "water_total": {"title": "水表总量", "bsonType": "int", "label": "水表总量", "defaultValue": 9999}, "ele_total": {"title": "电表总量", "bsonType": "int", "label": "电表总量", "defaultValue": 9999}, "gas_total": {"title": "燃气表总量", "bsonType": "int", "label": "燃气表总量", "defaultValue": 9999}, "water": {"title": "当前水表数", "bsonType": "int", "label": "当前水表数", "defaultValue": 0}, "ele": {"title": "当前电表数", "bsonType": "int", "label": "当前电表数", "defaultValue": 0}, "sum_detail": {"title": "总和金额详情", "bsonType": "object", "label": "总计详情"}, "config_layout": {"title": "配置的房型", "label": "配置的房型", "bsonType": "string", "foreignKey": "fangke_room_model._id", "defaultValue": ""}, "isVip": {"bsonType": "bool", "title": "是否开通会员", "description": "用于判断是否开通会员", "defaultValue": false, "label": "是否开通会员"}, "level": {"bsonType": "int", "label": "vip等级", "defaultValue": 0}, "tenant_id": {"bsonType": "string", "label": "租客id", "foreignKey": "fangke_tenants._id", "defaultValue": ""}, "over_days": {"bsonType": "int", "label": "逾期天数", "defaultValue": 0}, "message_count": {"bsonType": "int", "label": "通知次数", "description": "用于每月催缴，日常通知的短信次数", "defaultValue": 0}, "contract_count": {"bsonType": "int", "label": "电子合同", "description": "用于生成电子合同次数", "defaultValue": 0}}}