<template>
	<view class="utility-page">
		<!-- 底部统计信息 -->
		<view class="summary-info">
			<view class="summary-item">
				<text class="summary-label">总房间数：</text>
				<text class="summary-value">{{ totalRooms }}</text>
			</view>
			<view class="summary-item">
				<text class="summary-label">已抄表：</text>
				<text class="summary-value">{{ recordedRooms }}</text>
			</view>
			<view class="summary-item">
				<text class="summary-label">未抄表：</text>
				<text class="summary-value">{{ unrecordedRooms }}</text>
			</view>
		</view>
		<!-- 顶部标签区 -->
		<view class="top-tabs">
			<view
				v-for="(tab, index) in utilityTabs"
				:key="index"
				class="tab-item"
				:class="{ active: currentTab === index }"
				@click="switchTab(index)"
			>
				<text class="tab-text">{{ tab.name }}</text>
			</view>
		</view>

		<!-- 筛选区域 -->
		<view class="filter-area">
			<!-- 操作按钮区 -->
			<view class="action-buttons">
				<button class="action-btn add-btn" @click="addRecord">添加记录</button>
				<button class="action-btn import-btn" @click="importData">导入</button>
				<button class="action-btn export-btn" @click="exportData">导出</button>
				<button class="action-btn batch-btn" @click="batchSubmit">批量提交</button>
			</view>

			<!-- 筛选条件 -->
			<view class="filter-controls">
				<!-- 按表类型筛选 -->
				<template v-if="currentTab === 0">
					<view class="filter-group">
						<text class="filter-label">房源筛选</text>
						<view class="select-container">
							<uni-data-select
								v-model="selectedBuilding"
								:localdata="buildingOptions"
								placeholder="请选择房源"
								@change="onBuildingChange"
							/>
						</view>
					</view>

					<view class="filter-group">
						<text class="filter-label">设备筛选</text>
						<view class="select-container">
							<uni-data-select
								v-model="selectedDeviceType"
								:localdata="deviceTypeOptions"
								placeholder="请选择设备类型"
								@change="onDeviceTypeChange"
							/>
						</view>
					</view>

					<view class="filter-group">
						<text class="filter-label">抄表状态</text>
						<view class="select-container">
							<uni-data-select
								v-model="selectedRecordStatus"
								:localdata="recordStatusOptions"
								placeholder="请选择状态"
								@change="onRecordStatusChange"
							/>
						</view>
					</view>

					<view class="filter-group">
						<text class="filter-label">房间搜索</text>
						<view class="search-container">
							<input
								v-model="searchKeyword"
								class="search-input"
								placeholder="请输入房间号"
								@input="onSearchInput"
							/>
						</view>
					</view>
				</template>



				<!-- 待生成账单筛选 -->
				<template v-else-if="currentTab === 1">
					<view class="filter-group">
						<text class="filter-label">房源筛选</text>
						<view class="select-container">
							<uni-data-select
								v-model="selectedBuilding"
								:localdata="buildingOptions"
								placeholder="请选择房源"
								@change="onBuildingChange"
							/>
						</view>
					</view>

					<view class="filter-group">
						<text class="filter-label">水电表类型</text>
						<view class="select-container">
							<uni-data-select
								v-model="selectedDeviceType"
								:localdata="deviceTypeOptions"
								placeholder="请选择设备类型"
								@change="onDeviceTypeChange"
							/>
						</view>
					</view>

					<view class="filter-group">
						<text class="filter-label">房间搜索</text>
						<view class="search-container">
							<input
								v-model="searchKeyword"
								class="search-input"
								placeholder="请输入房间号"
								@input="onSearchInput"
							/>
						</view>
					</view>
				</template>
			</view>
		</view>

		<!-- 数据表格 -->
		<view class="data-table">
			<!-- 表头 -->
			<view class="table-header">
				<view class="header-cell building-cell">房源</view>
				<view class="header-cell room-cell">房间</view>
				<view class="header-cell type-cell">抄表类型</view>
				<view class="header-cell last-cell">上月读数</view>
				<view class="header-cell current-cell">本月读数</view>
				<view class="header-cell usage-cell">用量</view>
				<view class="header-cell status-cell">状态</view>
				<view class="header-cell action-cell">操作</view>
			</view>

			<!-- 数据行 -->
			<view class="table-body">
				<!-- 数据显示 -->
				<view
					v-for="item in filteredUtilityList"
					:key="`${item.room_id}_${item.type}_${item.date}`"
					class="table-row"
					:class="getRowClass(item)"
				>
					<view class="body-cell building-cell">{{ getBuildingName(item.building_id) }}</view>
					<view class="body-cell room-cell">{{ item.room_name }}</view>
					<view class="body-cell type-cell">
						<view class="type-badge" :class="getTypeClass(item.type)">
							{{ getTypeText(item.type) }}
						</view>
					</view>
					<view class="body-cell last-cell">
						<input
							v-model="item.last_num_display"
							class="meter-input"
							type="number"
							:disabled="item.is_submit"
							@input="onLastNumChange(item, $event)"
						/>
					</view>
					<view class="body-cell current-cell">
						<input
							v-model="item.num_display"
							class="meter-input"
							type="number"
							:disabled="item.is_submit"
							@input="onCurrentNumChange(item, $event)"
						/>
					</view>
					<view class="body-cell usage-cell">
						<text class="usage-text" :class="{ 'usage-highlight': item.usage > 0 }">
							{{ formatUsage(item.usage) }}{{ getUnitText(item.type) }}
						</text>
					</view>
					<view class="body-cell status-cell">
						<view class="status-badge" :class="getStatusClass(item)">
							{{ getStatusText(item) }}
						</view>
					</view>
					<view class="body-cell action-cell">
						<!-- 按表类型标签：显示提交账单按钮（已提交的不显示） -->
						<template v-if="currentTab === 0">
							<button
								v-if="item.is_update && !item.is_submit"
								class="action-btn submit-btn"
								@click="submitSingleRecord(item)"
								size="mini"
							>
								提交账单
							</button>
						</template>
						<!-- 待生成账单标签：显示生成账单按钮 -->
						<template v-else-if="currentTab === 1">
							<button
								class="action-btn generate-btn"
								@click="generateBill(item)"
							>
								生成账单
							</button>
						</template>
					</view>
				</view>


			</view>

			<!-- 空状态 -->
			<view v-if="filteredUtilityList.length === 0" class="empty-state">
				<image class="empty-icon" src="/static/empty.png" mode="aspectFit" />
				<text class="empty-text">{{ getEmptyText() }}</text>
			</view>
		</view>

		
	</view>
</template>
<script setup>
	import {
		ref,
		computed,
		onMounted
	} from 'vue'

	// 响应式数据
	const utilityList = ref([])
	const loading = ref(false)
	const currentTab = ref(0)
	const selectedBuilding = ref('')
	const selectedDeviceType = ref('')
	const selectedRecordStatus = ref('')
	const searchKeyword = ref('')

	// 标签配置
	const utilityTabs = ref([
		{ name: '按表类型', type: 'by_type' },
		{ name: '待生成账单', type: 'pending_bill' }
	])

	// 楼栋选项
	const buildingOptions = [
		{ text: '全部房源', value: '' },
		{ text: '公寓A栋', value: 'building_a' },
		{ text: '公寓B栋', value: 'building_b' },
		{ text: '公寓C栋', value: 'building_c' },
		{ text: '青年公寓1号楼', value: 'building_youth_1' },
		{ text: '青年公寓2号楼', value: 'building_youth_2' }
	]

	// 设备类型选项
	const deviceTypeOptions = [
		{ text: '全部设备', value: '' },
		{ text: '冷水表', value: 1 },
		{ text: '电表', value: 2 },
		{ text: '燃气表', value: 3 }
	]

	// 抄表状态选项
	const recordStatusOptions = [
		{ text: '全部状态', value: '' },
		{ text: '已抄表', value: 'recorded' },
		{ text: '未抄表', value: 'unrecorded' }
	]

	// 筛选后的水电表列表
	const filteredUtilityList = computed(() => {
		let filtered = [...utilityList.value]

		// 只显示当月数据
		const now = new Date()
		const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
		filtered = filtered.filter(item => item.date === currentMonth)

		// 按搜索关键词筛选
		if (searchKeyword.value) {
			const keyword = searchKeyword.value.toLowerCase()
			filtered = filtered.filter(item =>
				item.room_name?.toLowerCase().includes(keyword)
			)
		}

		// 按楼栋筛选
		if (selectedBuilding.value) {
			filtered = filtered.filter(item => item.building_id === selectedBuilding.value)
		}

		// 根据不同标签进行筛选
		if (currentTab.value === 0) {
			// 按表类型：显示所有记录，可按设备类型筛选
			if (selectedDeviceType.value !== '') {
				filtered = filtered.filter(item => item.type === selectedDeviceType.value)
			}

			// 按抄表状态筛选
			if (selectedRecordStatus.value === 'recorded') {
				filtered = filtered.filter(item => item.is_update)
			} else if (selectedRecordStatus.value === 'unrecorded') {
				filtered = filtered.filter(item => !item.is_update)
			}
		} else if (currentTab.value === 1) {
			// 待生成账单：只显示已抄但未提交的记录
			filtered = filtered.filter(item => item.is_update && !item.is_submit)

			// 可按设备类型筛选
			if (selectedDeviceType.value !== '') {
				filtered = filtered.filter(item => item.type === selectedDeviceType.value)
			}
		}

		return filtered
	})



	// 统计信息
	const totalRooms = computed(() => {
		const uniqueRooms = new Set(filteredUtilityList.value.map(item => item.room_id))
		return uniqueRooms.size
	})

	const recordedRooms = computed(() => {
		const recordedRoomIds = new Set(
			filteredUtilityList.value
				.filter(item => item.is_update)
				.map(item => item.room_id)
		)
		return recordedRoomIds.size
	})

	const unrecordedRooms = computed(() => {
		return totalRooms.value - recordedRooms.value
	})

	// 获取水电表记录数据
	const getUtilityList = async () => {
		try {
			loading.value = true
			// 这里应该调用云函数获取水电表记录
			// const utilityObj = uniCloud.importObject('utility')
			// const res = await utilityObj.getUtilityRecords()

			// 使用模拟数据 - 当月数据
			const now = new Date()
			const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`

			const mockData = [
				{
					_id: '1',
					room_id: 'room_101',
					room_name: '101',
					building_id: 'building_a',
					type: 1, // 冷水表
					date: currentMonth,
					last_num: 100000, // 1000.00
					num: 200000, // 2000.00
					usage: 100000, // 1000.00
					is_submit: false,
					is_update: true,
					unit: 1,
					last_num_display: '1000.00',
					num_display: '2000.00'
				},
				{
					_id: '2',
					room_id: 'room_101',
					room_name: '101',
					building_id: 'building_a',
					type: 2, // 电表
					date: currentMonth,
					last_num: 50000, // 500.00
					num: 80000, // 800.00
					usage: 30000, // 300.00
					is_submit: false,
					is_update: true,
					unit: 1,
					last_num_display: '500.00',
					num_display: '800.00'
				},
				{
					_id: '3',
					room_id: 'room_102',
					room_name: '102',
					building_id: 'building_a',
					type: 1, // 冷水表
					date: currentMonth,
					last_num: 0,
					num: 0,
					usage: 0,
					is_submit: false,
					is_update: false,
					unit: 1,
					last_num_display: '0',
					num_display: '0'
				},
				{
					_id: '4',
					room_id: 'room_102',
					room_name: '102',
					building_id: 'building_a',
					type: 2, // 电表
					date: currentMonth,
					last_num: 0,
					num: 0,
					usage: 0,
					is_submit: false,
					is_update: false,
					unit: 1,
					last_num_display: '0',
					num_display: '0'
				},
				{
					_id: '5',
					room_id: 'room_103',
					room_name: '103',
					building_id: 'building_a',
					type: 1, // 冷水表
					date: currentMonth,
					last_num: 20000, // 200.00
					num: 25000, // 250.00
					usage: 5000, // 50.00
					is_submit: true,
					is_update: true,
					unit: 1,
					last_num_display: '200.00',
					num_display: '250.00'
				},
				{
					_id: '6',
					room_id: 'room_103',
					room_name: '103',
					building_id: 'building_a',
					type: 2, // 电表
					date: currentMonth,
					last_num: 100000, // 1000.00
					num: 150000, // 1500.00
					usage: 50000, // 500.00
					is_submit: true,
					is_update: true,
					unit: 1,
					last_num_display: '1000.00',
					num_display: '1500.00'
				},
				{
					_id: '7',
					room_id: 'room_104',
					room_name: '104',
					building_id: 'building_a',
					type: 1, // 冷水表
					date: currentMonth,
					last_num: 30000, // 300.00
					num: 35000, // 350.00
					usage: 5000, // 50.00
					is_submit: false,
					is_update: true,
					unit: 1,
					last_num_display: '300.00',
					num_display: '350.00'
				},
				{
					_id: '8',
					room_id: 'room_104',
					room_name: '104',
					building_id: 'building_a',
					type: 2, // 电表
					date: currentMonth,
					last_num: 0,
					num: 0,
					usage: 0,
					is_submit: false,
					is_update: false,
					unit: 1,
					last_num_display: '0',
					num_display: '0'
				},
				{
					_id: '9',
					room_id: 'room_105',
					room_name: '105',
					building_id: 'building_a',
					type: 1, // 冷水表
					date: currentMonth,
					last_num: 0,
					num: 0,
					usage: 0,
					is_submit: false,
					is_update: false,
					unit: 1,
					last_num_display: '0',
					num_display: '0'
				}
			]

			utilityList.value = mockData
		} catch (error) {
			console.error('获取水电表记录失败:', error)
			uni.showToast({
				title: '获取数据失败',
				icon: 'error'
			})
		} finally {
			loading.value = false
		}
	}
	// 事件处理
	const switchTab = (index) => {
		currentTab.value = index
	}

	const onBuildingChange = (value) => {
		selectedBuilding.value = value
	}

	const onDeviceTypeChange = (value) => {
		selectedDeviceType.value = value
	}

	const onRecordStatusChange = (value) => {
		selectedRecordStatus.value = value
	}

	const onSearchInput = () => {
		// 实时搜索已通过computed属性实现
	}

	// 输入框事件处理
	const onLastNumChange = (item, event) => {
		const value = parseFloat(event.detail.value) || 0
		item.last_num = Math.round(value * 100) // 转换为分
		item.last_num_display = value.toFixed(2)
		calculateUsage(item)
		updateRecordStatus(item)
	}

	const onCurrentNumChange = (item, event) => {
		const value = parseFloat(event.detail.value) || 0
		item.num = Math.round(value * 100) // 转换为分
		item.num_display = value.toFixed(2)
		calculateUsage(item)
		updateRecordStatus(item)
	}

	// 计算用量
	const calculateUsage = (item) => {
		if (item.num >= item.last_num) {
			item.usage = item.num - item.last_num
		} else {
			item.usage = 0
		}
	}

	// 更新记录状态
	const updateRecordStatus = (item) => {
		item.is_update = item.num > 0 || item.last_num > 0
	}

	// 操作方法
	const addRecord = () => {
		console.log('添加记录')
		uni.showToast({
			title: '添加记录功能待开发',
			icon: 'none'
		})
	}

	const importData = () => {
		console.log('导入数据')
		uni.showToast({
			title: '导入功能待开发',
			icon: 'none'
		})
	}

	const exportData = () => {
		console.log('导出数据')
		uni.showToast({
			title: '导出功能待开发',
			icon: 'none'
		})
	}

	const batchSubmit = () => {
		console.log('批量提交')
		const unsubmittedRecords = filteredUtilityList.value.filter(item => !item.is_submit && item.is_update)

		if (unsubmittedRecords.length === 0) {
			uni.showToast({
				title: '没有可提交的记录',
				icon: 'none'
			})
			return
		}

		uni.showModal({
			title: '确认提交',
			content: `确定要提交 ${unsubmittedRecords.length} 条记录吗？`,
			success: (res) => {
				if (res.confirm) {
					// 这里应该调用云函数批量提交
					unsubmittedRecords.forEach(item => {
						item.is_submit = true
					})

					uni.showToast({
						title: '提交成功',
						icon: 'success'
					})
				}
			}
		})
	}

	// 单条记录提交账单
	const submitSingleRecord = (item) => {
		console.log('提交单条记录', item)

		if (!item.is_update) {
			uni.showToast({
				title: '请先完成抄表',
				icon: 'none'
			})
			return
		}

		if (item.is_submit) {
			uni.showToast({
				title: '该记录已提交',
				icon: 'none'
			})
			return
		}

		uni.showModal({
			title: '确认提交',
			content: `确定要提交房间 ${item.room_name} 的${getTypeText(item.type)}记录吗？`,
			success: (res) => {
				if (res.confirm) {
					// 这里应该调用云函数提交单条记录
					item.is_submit = true

					uni.showToast({
						title: '提交成功',
						icon: 'success'
					})
				}
			}
		})
	}

	// 生成账单
	const generateBill = (item) => {
		console.log('生成账单', item)

		if (!item.is_update || !item.is_submit) {
			uni.showToast({
				title: '记录状态异常',
				icon: 'none'
			})
			return
		}

		uni.showModal({
			title: '确认生成账单',
			content: `确定要为房间 ${item.room_name} 生成${getTypeText(item.type)}账单吗？`,
			success: (res) => {
				if (res.confirm) {
					// 这里应该调用云函数生成账单
					uni.showToast({
						title: '账单生成成功',
						icon: 'success'
					})

					// 可以跳转到账单详情页面
					// uni.navigateTo({
					//     url: `/pages/bill/detail?id=${item._id}`
					// })
				}
			}
		})
	}
	// 工具函数
	const getTypeText = (type) => {
		const typeMap = {
			1: '冷水表',
			2: '电表',
			3: '燃气'
		}
		return typeMap[type] || '未知'
	}

	const getTypeClass = (type) => {
		const classMap = {
			1: 'type-water',
			2: 'type-electric',
			3: 'type-gas'
		}
		return classMap[type] || ''
	}

	const getUnitText = (type) => {
		const unitMap = {
			1: '吨',
			2: '度',
			3: '立方米'
		}
		return unitMap[type] || ''
	}

	const getStatusText = (item) => {
		if (item.is_submit) {
			return '已提交'
		} else if (item.is_update) {
			return '已抄表'
		} else {
			return '未抄表'
		}
	}

	const getStatusClass = (item) => {
		if (item.is_submit) {
			return 'status-submitted'
		} else if (item.is_update) {
			return 'status-recorded'
		} else {
			return 'status-unrecorded'
		}
	}

	const getRowClass = (item) => {
		if (item.is_submit) {
			return 'row-submitted'
		} else if (item.is_update) {
			return 'row-recorded'
		} else {
			return 'row-unrecorded'
		}
	}

	const formatUsage = (usage) => {
		if (!usage) return '0'
		return (usage / 100).toFixed(2)
	}



	const getBuildingName = (buildingId) => {
		const building = buildingOptions.find(item => item.value === buildingId)
		return building ? building.text : '未知房源'
	}

	const getEmptyText = () => {
		if (loading.value) {
			return '加载中...'
		}

		if (searchKeyword.value) {
			return '未找到匹配的房间'
		}

		if (currentTab.value === 0) {
			return '暂无水电表记录'
		} else {
			return '暂无待生成账单记录'
		}
	}

	// 页面加载时获取数据
	onMounted(async () => {
		await getUtilityList()
	})
</script>
<style scoped>
	.utility-page {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding: 20rpx;
	}

	/* 顶部标签区 */
	.top-tabs {
		display: flex;
		background: white;
		border-radius: 8rpx 8rpx 0 0;
		border-bottom: 1rpx solid #e0e0e0;
		margin-bottom: 0;
		overflow-x: auto;
	}

	.tab-item {
		flex: 1;
		min-width: 120rpx;
		padding: 25rpx 15rpx;
		text-align: center;
		cursor: pointer;
		position: relative;
		transition: all 0.3s ease;
		border-bottom: 3rpx solid transparent;
	}

	.tab-item.active {
		background: #f0f9ff;
		border-bottom-color: #01B862;
	}

	.tab-text {
		font-size: 26rpx;
		color: #666;
		font-weight: 500;
	}

	.tab-item.active .tab-text {
		color: #01B862;
		font-weight: 600;
	}

	/* 筛选区域 */
	.filter-area {
		background: white;
		padding: 30rpx;
		border-radius: 0 0 8rpx 8rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	/* 操作按钮区 */
	.action-buttons {
		display: flex;
		gap: 15rpx;
		margin-bottom: 30rpx;
		flex-wrap: wrap;
	}

	.action-btn {
		padding: 15rpx 25rpx;
		border-radius: 6rpx;
		font-size: 26rpx;
		font-weight: 500;
		border: none;
		cursor: pointer;
		transition: all 0.3s ease;
		min-width: 100rpx;
	}

	.add-btn {
		background: #01B862;
		color: white;
	}

	.add-btn:hover {
		background: #019954;
	}

	.import-btn {
		background: #2196F3;
		color: white;
	}

	.import-btn:hover {
		background: #1976D2;
	}

	.export-btn {
		background: #FF9800;
		color: white;
	}

	.export-btn:hover {
		background: #F57C00;
	}

	.batch-btn {
		background: #9C27B0;
		color: white;
	}

	.batch-btn:hover {
		background: #7B1FA2;
	}

	/* 筛选控件 */
	.filter-controls {
		display: flex;
		flex-wrap: wrap;
		gap: 30rpx;
		align-items: flex-end;
	}

	.filter-group {
		display: flex;
		flex-direction: column;
		min-width: 200rpx;
		flex: 1;
	}

	.filter-label {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 15rpx;
		font-weight: 500;
	}

	.date-picker-container,
	.select-container,
	.search-container {
		width: 100%;
	}

	.search-input {
		width: 100%;
		height: 70rpx;
		padding: 0 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 6rpx;
		font-size: 28rpx;
		background: white;
	}

	.search-input:focus {
		border-color: #01B862;
	}
	/* 数据表格 */
	.data-table {
		width: 100%;
				background: white;
				border-radius: 8rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
				overflow: hidden;
	}

	.table-header {
		width: 100%;
				display: grid;
				grid-template-columns: auto auto auto auto auto auto auto auto;
				justify-content: space-around;
				align-items: center;
				background: #f8f9fa;
				border-bottom: 2rpx solid #e0e0e0;
				font-weight: 600;
				color: #333;
	}

	.table-body {
		width: 100%;
				flex: 1;
				overflow-y: auto;
				justify-content: space-around;
	}

	.table-row {
		justify-content: space-around;
				display: grid;
				grid-template-columns: auto auto auto auto auto auto auto auto;
				align-items: center;
				border-bottom: 1rpx solid #f0f0f0;
				transition: background-color 0.3s ease;
	}

	.table-row:hover {
		background: #f8f9fa;
	}

	.table-row.row-submitted {
		background: #e8f5e8;
	}

	.table-row.row-recorded {
		background: #fff8e1;
	}

	.table-row.row-unrecorded {
		background: #fff5f5;
	}

	.table-row:last-child {
		border-bottom: none;
	}

	.header-cell,
	.body-cell {
		padding: 25rpx 15rpx;
		font-size: 28rpx;
		text-align: center;
		word-break: break-all;
	}

	.building-cell {
		width: 150rpx;
		flex-shrink: 0;
		font-weight: 600;
	}

	.room-cell {
		width: 120rpx;
		flex-shrink: 0;
		font-weight: 600;
	}

	.type-cell {
		width: 150rpx;
		flex-shrink: 0;
	}

	.last-cell,
	.current-cell {
		width: 150rpx;
		flex-shrink: 0;
	}

	.usage-cell {
		width: 150rpx;
		flex-shrink: 0;
	}

	.status-cell {
		width: 150rpx;
		flex-shrink: 0;
	}

	.action-cell {
		width: 150rpx;
		flex-shrink: 0;
	}



	/* 输入框 */
	.meter-input {
		width: 100%;
		height: 60rpx;
		padding: 0 15rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 4rpx;
		font-size: 26rpx;
		text-align: center;
		background: white;
	}

	.meter-input:focus {
		border-color: #01B862;
	}

	.meter-input:disabled {
		background: #f5f5f5;
		color: #999;
		cursor: not-allowed;
	}

	/* 类型标签 */
	.type-badge {
		display: inline-block;
		padding: 8rpx 16rpx;
		border-radius: 16rpx;
		font-size: 24rpx;
		font-weight: 500;
		text-align: center;
		min-width: 60rpx;
	}

	.type-water {
		background: #e3f2fd;
		color: #1976d2;
	}

	.type-electric {
		background: #fff3e0;
		color: #f57c00;
	}

	.type-gas {
		background: #f3e5f5;
		color: #7b1fa2;
	}

	/* 状态标签 */
	.status-badge {
		display: inline-block;
		padding: 8rpx 16rpx;
		border-radius: 16rpx;
		font-size: 24rpx;
		font-weight: 500;
		text-align: center;
		min-width: 80rpx;
	}

	.status-submitted {
		background: #d4edda;
		color: #155724;
	}

	.status-recorded {
		background: #fff3cd;
		color: #856404;
	}

	.status-unrecorded {
		background: #f8d7da;
		color: #721c24;
	}

	/* 用量显示 */
	.usage-text {
		font-weight: 600;
		color: #666;
	}

	.usage-text.usage-highlight {
		color: #01B862;
	}

	/* 空状态 */
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 400rpx;
		color: #999;
		padding: 40rpx;
	}

	.empty-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
		opacity: 0.5;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
		text-align: center;
	}

	/* 操作按钮样式 */
	.submit-btn {
		background: #01B862;
		color: white;
		border: none;
		padding: 8rpx 16rpx;
		border-radius: 4rpx;
		font-size: 24rpx;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.submit-btn:hover {
		background: #019954;
	}

	.generate-btn {
		background: #FF9800;
		color: white;
		border: none;
		padding: 8rpx 16rpx;
		border-radius: 4rpx;
		font-size: 24rpx;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.generate-btn:hover {
		background: #F57C00;
	}
	/* 底部统计信息 */
	.summary-info {
		background: white;
		padding: 30rpx;
		border-radius: 8rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		display: flex;
		justify-content: space-around;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.summary-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		min-width: 120rpx;
	}

	.summary-label {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 10rpx;
	}

	.summary-value {
		font-size: 32rpx;
		font-weight: 600;
		color: #01B862;
	}

	/* 响应式设计 */
	@media (max-width: 1200px) {
		.top-tabs {
			flex-wrap: wrap;
		}

		.action-buttons {
			justify-content: center;
		}

		.filter-controls {
			flex-direction: column;
			align-items: stretch;
			gap: 20rpx;
		}

		.filter-group {
			min-width: auto;
			width: 100%;
		}
	}

	@media (max-width: 768px) {
		.utility-page {
			padding: 10rpx;
		}

		.tab-item {
			min-width: 100rpx;
			padding: 20rpx 10rpx;
		}

		.tab-text {
			font-size: 24rpx;
		}

		.filter-area {
			padding: 20rpx;
		}

		.action-buttons {
			gap: 10rpx;
		}

		.action-btn {
			font-size: 24rpx;
		}

		.table-header,
		.table-row {
			font-size: 24rpx;
		}

		.header-cell,
		.body-cell {
			padding: 15rpx 8rpx;
		}

		.building-cell {
			width: 80rpx;
		}

		.room-cell {
			width: 60rpx;
		}

		.type-cell {
			width: 80rpx;
		}

		.last-cell,
		.current-cell {
			width: 100rpx;
		}

		.usage-cell,
		.status-cell {
			width: 80rpx;
		}

		.action-cell {
			width: 70rpx;
		}

		.meter-input {
			height: 50rpx;
			font-size: 24rpx;
		}

		.type-badge,
		.status-badge {
			font-size: 20rpx;
			padding: 6rpx 12rpx;
			min-width: 50rpx;
		}

		.submit-btn,
		.generate-btn {
			font-size: 20rpx;
			padding: 6rpx 10rpx;
		}

		.summary-info {
			padding: 20rpx;
		}

		.summary-item {
			min-width: 80rpx;
		}

		.summary-label {
			font-size: 22rpx;
		}

		.summary-value {
			font-size: 28rpx;
		}
	}
</style>
